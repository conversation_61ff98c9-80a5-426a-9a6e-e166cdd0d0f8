-------------------------------WChat 聊天增强模块-------------------------------
-- 独立聊天增强模块，移除ACE3依赖
-- 作者: VGGFAC
-- 版本: 2.0.0

local addonName, addon = ...

-- 模块配置
local WChat = {}
-- 暴露为全局变量供配置界面检测
_G.WChat_Config = {
    -- 默认配置
    ShortChannel = true,        -- 频道标签精简
    EnableEmoteInput = true,    -- 启用表情输入
    EmoteIconSize = 16,         -- 表情图标大小
    EmoteIconListSize = 20,     -- 表情列表图标大小
    EmoteOffsetX = 0,           -- 表情面板X偏移
    EmoteOffsetY = 30,          -- 表情面板Y偏移
    UseVertical = false,        -- 聊天条垂直布局

    UseTopInput = false,        -- 输入框在上方
    ChatBarOffsetX = 0,         -- 聊天条X偏移
    ChatBarOffsetY = 0,         -- 聊天条Y偏移
    DistanceVertical = -25,     -- 垂直间距
    DistanceHorizontal = 25,    -- 水平间距
    AlphaOnEnter = 1.0,         -- 鼠标悬停透明度
    AlphaOnLeave = 0.7,         -- 鼠标离开透明度
    Position = nil,             -- 聊天条位置
    EnableTimestampCopy = true, -- 启用时间戳点击复制
    TimestampFormat = "%H:%M:%S", -- 时间戳格式
    TimestampColor = { 1.0, 1.0, 1.0 }, -- 时间戳颜色
    -- 插件按钮显示控制
    ShowBiaoGeButton = true,    -- 显示BiaoGe按钮
    ShowBiaoGeAIButton = true,  -- 显示BiaoGeAI按钮
    ShowAtlasButton = true,     -- 显示Atlas按钮
    ShowMeetingHornButton = true, -- 显示MeetingHorn按钮
    -- 频道隐藏设置
    HiddenChannels = {}
}

-- 频道配置映射
local CHANNEL_CONFIG = {
    MAPPINGS = {
        chnGen = "综合",
        chnTrade = "交易",
        chnLFG = "寻求组队",
        world = "大脚世界频道"
    }
}

-- 频道显示/隐藏管理函数
local function IsChannelShown(channelName)
    local channels = {GetChatWindowChannels(DEFAULT_CHAT_FRAME:GetID() or 1)}
    for i = 1, #channels, 2 do
        if channels[i] == channelName then return true end
    end
    return false
end

local function ToggleChannelShowHide(channelName)
    local isShown = IsChannelShown(channelName)
    if isShown then
        ChatFrame_RemoveChannel(DEFAULT_CHAT_FRAME, channelName)
        _G.WChat_Config.HiddenChannels[channelName] = true
        print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r 已隐藏频道: " .. channelName)
    else
        ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, channelName)
        _G.WChat_Config.HiddenChannels[channelName] = nil
        print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r 已显示频道: " .. channelName)
    end
end

local function UpdateChannelXIcons()
    if not WChatBar then return end

    for _, child in pairs({WChatBar:GetChildren()}) do
        if child.X and child.buttonName and CHANNEL_CONFIG.MAPPINGS[child.buttonName] then
            local channelName = CHANNEL_CONFIG.MAPPINGS[child.buttonName]
            child.X:SetShown(not IsChannelShown(channelName))
        end
    end
end

-- 获取语言
local Language = GetLocale()
if (Language == "zhTW") then
    --公会
    CHAT_GUILD_GET = "|Hchannel:GUILD|h[公會]|h %s: "
    CHAT_OFFICER_GET = "|Hchannel:OFFICER|h[官員]|h %s: "
    
    --团队
    CHAT_RAID_GET = "|Hchannel:RAID|h[團隊]|h %s: "
    CHAT_RAID_WARNING_GET = "[通知] %s: "
    CHAT_RAID_LEADER_GET = "|Hchannel:RAID|h[團長]|h %s: "
    
    --队伍
    CHAT_PARTY_GET = "|Hchannel:PARTY|h[隊伍]|h %s: "
    CHAT_PARTY_LEADER_GET = "|Hchannel:PARTY|h[隊長]|h %s: "
    CHAT_PARTY_GUIDE_GET = "|Hchannel:PARTY|h[向導]|h %s: "
    
    --战场
    CHAT_BATTLEGROUND_GET = "|Hchannel:BATTLEGROUND|h[戰場]|h %s: "
    CHAT_BATTLEGROUND_LEADER_GET = "|Hchannel:BATTLEGROUND|h[領袖]|h %s: "
    
    --说 / 喊
    CHAT_SAY_GET = "%s: "
    CHAT_YELL_GET = "%s: "
    
    --密语
    CHAT_WHISPER_INFORM_GET = "發送給%s: "
    CHAT_WHISPER_GET = "%s悄悄話: "
    
    --flags
    CHAT_FLAG_AFK = "[暫離] "
    CHAT_FLAG_DND = "[勿擾] "
    CHAT_FLAG_GM = "[GM] "
elseif (Language == "zhCN") then
    --公会
    CHAT_GUILD_GET = "|Hchannel:GUILD|h[公会]|h %s: "
    CHAT_OFFICER_GET = "|Hchannel:OFFICER|h[官员]|h %s: "
    
    --团队
    CHAT_RAID_GET = "|Hchannel:RAID|h[团队]|h %s: "
    CHAT_RAID_WARNING_GET = "[通知] %s: "
    CHAT_RAID_LEADER_GET = "|Hchannel:RAID|h[团长]|h %s: "
    
    --队伍
    CHAT_PARTY_GET = "|Hchannel:PARTY|h[队伍]|h %s: "
    CHAT_PARTY_LEADER_GET = "|Hchannel:PARTY|h[队长]|h %s: "
    CHAT_PARTY_GUIDE_GET = "|Hchannel:PARTY|h[向导]:|h %s: "
    
    --战场
    CHAT_BATTLEGROUND_GET = "|Hchannel:BATTLEGROUND|h[副本]|h %s: "
    CHAT_BATTLEGROUND_LEADER_GET = "|Hchannel:BATTLEGROUND|h[领袖]|h %s: "
    
    --密语
    CHAT_WHISPER_INFORM_GET = "发送给%s: "
    CHAT_WHISPER_GET = "%s悄悄的说: "
    CHAT_BN_WHISPER_INFORM_GET = "发送给%s: "
    CHAT_BN_WHISPER_GET = "悄悄的说%s: "
    
    --说 / 喊
    CHAT_SAY_GET = "%s: "
    CHAT_YELL_GET = "%s: "
    
    --flags
    CHAT_FLAG_AFK = "[暂离] "
    CHAT_FLAG_DND = "[勿扰] "
    CHAT_FLAG_GM = "[GM] "
else
    CHAT_GUILD_GET = "|Hchannel:GUILD|hG|h %s "
    CHAT_OFFICER_GET = "|Hchannel:OFFICER|hO|h %s "
    CHAT_RAID_GET = "|Hchannel:RAID|hR|h %s "
    CHAT_RAID_WARNING_GET = "RW %s "
    CHAT_RAID_LEADER_GET = "|Hchannel:RAID|hRL|h %s "
    CHAT_PARTY_GET = "|Hchannel:PARTY|hP|h %s "
    CHAT_PARTY_LEADER_GET = "|Hchannel:PARTY|hPL|h %s "
    CHAT_PARTY_GUIDE_GET = "|Hchannel:PARTY|hPG|h %s "
    CHAT_BATTLEGROUND_GET = "|Hchannel:BATTLEGROUND|hB|h %s "
    CHAT_BATTLEGROUND_LEADER_GET = "|Hchannel:BATTLEGROUND|hBL|h %s "
    CHAT_WHISPER_INFORM_GET = "to %s "
    CHAT_WHISPER_GET = "from %s "
    CHAT_BN_WHISPER_INFORM_GET = "to %s "
    CHAT_BN_WHISPER_GET = "from %s "
    CHAT_SAY_GET = "%s "
    CHAT_YELL_GET = "%s "
    CHAT_FLAG_AFK = "[AFK] "
    CHAT_FLAG_DND = "[DND] "
    CHAT_FLAG_GM = "[GM] "
end

--================================公共频道和自定义频道精简================================--
local gsub = _G.string.gsub
local newAddMsg = {}
local chn = {
    "%[%d+%. General.-%]",
    "%[%d+%. Trade.-%]",
    "%[%d+%. LocalDefense.-%]",
    "%[%d+%. LookingForGroup%]",
    "%[%d+%. WorldDefense%]",
    "%[%d+%. GuildRecruitment.-%]",
    "%[%d+%. BigFootChannel.-%]",
    "%[%d+%. CustomChannel.-%]" -- 自定义频道英文名随便填写
}

local rplc = {
    "[GEN]",
    "[TR]",
    "[WD]",
    "[LD]",
    "[LFG]",
    "[GR]",
    "[BFC]",
    "[CL]" -- 英文缩写
}

if (Language == "zhCN") then ---国服
    rplc[1] = "[%1综]"
    rplc[2] = "[%1交]"
    rplc[3] = "[%1防]"
    rplc[4] = "[%1组]"
    rplc[5] = "[%1守]"
    rplc[6] = "[%1招]"
    rplc[7] = "[%1世]"
    rplc[8] = "[%1自定义]" -- 自定义频道缩写请自行修改
elseif (Language == "zhTW") then ---台服
    rplc[1] = "[%1綜合]"
    rplc[2] = "[%1貿易]"
    rplc[3] = "[%1防務]"
    rplc[4] = "[%1組隊]"
    rplc[5] = "[%1守備]"
    rplc[6] = "[%1招募]"
    rplc[7] = "[%1世界]"
    rplc[8] = "[%1自定义]" -- 自定义频道缩写请自行修改
end

if Language == "zhCN" then
    ---------------------------------------- 国服简体中文 ---------------------------------------------
    chn[1] = "%[%d+%. 综合.-%]"
    chn[2] = "%[%d+%. 交易.-%]"
    chn[3] = "%[%d+%. 本地防务.-%]"
    chn[4] = "%[%d+%. 寻求组队%]"
    chn[5] = "%[%d+%. 世界防务%]"
    chn[6] = "%[%d+%. 公会招募.-%]"
    chn[7] = "%[%d+%. 大脚世界频道.-%]"
    chn[8] = "%[%d+%. 自定义频道.-%]" -- 请修改频道名对应你游戏里的频道
elseif Language == "zhTW" then
    ---------------------------------------- 台服繁体中文 ---------------------------------------------
    chn[1] = "%[%d+%. 綜合.-%]"
    chn[2] = "%[%d+%. 貿易.-%]"
    chn[3] = "%[%d+%. 本地防務.-%]"
    chn[4] = "%[%d+%. 尋求組隊%]"
    chn[5] = "%[%d+%. 世界防務%]"
    chn[6] = "%[%d+%. 公會招募.-%]"
    chn[7] = "%[%d+%. 大脚世界频道.-%]"
    chn[8] = "%[%d+%. 自定义频道.-%]" -- 请修改频道名对应你游戏里的频道
end

local rules = {
        --!!不要改
        {pat = "|c%x+|HChatCopy|h.-|h|r", repl = ""},
        {pat = "|c%x%x%x%x%x%x%x%x(.-)|r", repl = "%1"},
        --左鍵
        {pat = "|Hchannel:.-|h.-|h", repl = "", button = "LeftButton"},
        {pat = "|Hplayer:.-|h.-|h" .. ":", repl = "", button = "LeftButton"},
        {pat = "|Hplayer:.-|h.-|h" .. "：", repl = "", button = "LeftButton"},
        {pat = "|HBNplayer:.-|h.-|h" .. ":", repl = "", button = "LeftButton"},
        {pat = "|HBNplayer:.-|h.-|h" .. "：", repl = "", button = "LeftButton"},
        --右鍵
        {pat = "|Hchannel:.-|h(.-)|h", repl = "%1", button = "RightButton"},
        {pat = "|Hplayer:.-|h(.-)|h", repl = "%1", button = "RightButton"},
        {pat = "|HBNplayer:.-|h(.-)|h", repl = "%1", button = "RightButton"},
        --!!不要改
        {pat = "|H.-|h(.-)|h", repl = "%1"},
        {pat = "|TInterface\\TargetingFrame\\UI%-RaidTargetingIcon_(%d):0|t", repl = "{rt%1}"},
        {pat = "|T.-|t", repl = ""},
        {pat = "^%s+", repl = ""}
}

-- 文字修改函数
function WChat:AddMessage(text, ...)
    -- 频道标签精简
    if (type(text) ~= "string") then
        text = tostring(text)
    end
    for i = 1, 8 do -- 对应上面几个频道(如果有9个频道就for i = 1, 9 do)
        text = text:gsub(chn[i], rplc[i])
    end
    text = text:gsub("%[(%d0?)%. .-%]", "%1.")
    return self.DefaultAddMessage(self, text, ...)
end

-- 初始化频道信息精简模块
function WChat:InitChannel()
    -- 先恢复所有聊天框的原始AddMessage函数
    for i = 1, (NUM_CHAT_WINDOWS or 10) do
        if i ~= 2 then
            local cf = _G['ChatFrame' .. i]
            if cf.DefaultAddMessage then
                cf.AddMessage = cf.DefaultAddMessage
            end
        end
    end

    -- 如果启用频道标签精简，则重新hook AddMessage函数
    if _G.WChat_Config.ShortChannel then
        for i = 1, (NUM_CHAT_WINDOWS or 10) do
            if i ~= 2 then
                local cf = _G['ChatFrame' .. i]
                if not cf.DefaultAddMessage then
                    cf.DefaultAddMessage = cf.AddMessage
                end
                cf.AddMessage = self.AddMessage
            end
        end
    end
end
-------------------------------聊天复制------------------------------------

local lines = {}

local chatCopyFrame = CreateFrame("Frame", "ChatCopyFrame", UIParent, BackdropTemplateMixin and "BackdropTemplate" or nil)
chatCopyFrame:SetPoint("CENTER", UIParent, "CENTER")
chatCopyFrame:SetSize(700, 400)
chatCopyFrame:Hide()
chatCopyFrame:SetFrameStrata("DIALOG")
chatCopyFrame.close = CreateFrame("Button", nil, chatCopyFrame, "UIPanelCloseButton")
chatCopyFrame.close:SetPoint("TOPRIGHT", chatCopyFrame, "TOPRIGHT")
chatCopyFrame:SetBackdrop({
    bgFile = "Interface/DialogFrame/UI-DialogBox-Background",
    edgeFile = "Interface/DialogFrame/UI-DialogBox-Border",
    tile = true,
    tileSize = 16,
    edgeSize = 16,
    insets = {left = 4, right = 4, top = 4, bottom = 4}
})

local scrollArea = CreateFrame("ScrollFrame", "ChatCopyScrollFrame", chatCopyFrame, "UIPanelScrollFrameTemplate")
scrollArea:SetPoint("TOPLEFT", chatCopyFrame, "TOPLEFT", 10, -30)
scrollArea:SetPoint("BOTTOMRIGHT", chatCopyFrame, "BOTTOMRIGHT", -30, 10)

local editBox = CreateFrame("EditBox", nil, chatCopyFrame)
editBox:SetMultiLine(true)
editBox:SetMaxLetters(99999)
editBox:EnableMouse(true)
editBox:SetAutoFocus(false)
editBox:SetFontObject(ChatFontNormal)
editBox:SetWidth(scrollArea:GetWidth())
editBox:SetHeight(270)
editBox:SetScript("OnEscapePressed", function(f)f:GetParent():GetParent():Hide()f:SetText("") end)
scrollArea:SetScrollChild(editBox)

function WChat:CopyFunc()
    local cf = SELECTED_CHAT_FRAME
    local _, size = cf:GetFont()
    FCF_SetChatWindowFontSize(cf, cf, .01)
    local ct = 1
    for i = select("#", cf.FontStringContainer:GetRegions()), 1, -1 do
        local region = select(i, cf.FontStringContainer:GetRegions())
        if region:GetObjectType() == "FontString" then
            if region:GetText() ~= nil then
                lines[ct] = tostring(region:GetText())
                ct = ct + 1
            end
        end
    end
    local lineCt = ct - 1
    local text = table.concat(lines, "\n", 1, lineCt)
    FCF_SetChatWindowFontSize(cf, cf, size)
    chatCopyFrame:Show()
    editBox:SetText(text)
    editBox:HighlightText(0)
    wipe(lines)
end
----------------------------聊天表情相关代码-----------------------------

-- 表情选择器框架
local EmoteTableFrame

-- 表情解析规则
local fmtstring

-- 自定义表情开始的序号
local customEmoteStartIndex = 9

local emotes = {
        --原版暴雪提供的8个图标
        {"{rt1}", [=[Interface\TargetingFrame\UI-RaidTargetingIcon_1]=]},
        {"{rt2}", [=[Interface\TargetingFrame\UI-RaidTargetingIcon_2]=]},
        {"{rt3}", [=[Interface\TargetingFrame\UI-RaidTargetingIcon_3]=]},
        {"{rt4}", [=[Interface\TargetingFrame\UI-RaidTargetingIcon_4]=]},
        {"{rt5}", [=[Interface\TargetingFrame\UI-RaidTargetingIcon_5]=]},
        {"{rt6}", [=[Interface\TargetingFrame\UI-RaidTargetingIcon_6]=]},
        {"{rt7}", [=[Interface\TargetingFrame\UI-RaidTargetingIcon_7]=]},
        {"{rt8}", [=[Interface\TargetingFrame\UI-RaidTargetingIcon_8]=]},
        --自定义表情
        {"{天使}", [=[Interface\Addons\WanTiny\Textures\Emotion\Angel]=]},
        {"{生气}", [=[Interface\Addons\WanTiny\Textures\Emotion\Angry]=]},
        {"{大笑}", [=[Interface\Addons\WanTiny\Textures\Emotion\Biglaugh]=]},
        {"{鼓掌}", [=[Interface\Addons\WanTiny\Textures\Emotion\Clap]=]},
        {"{酷}", [=[Interface\Addons\WanTiny\Textures\Emotion\Cool]=]},
        {"{哭}", [=[Interface\Addons\WanTiny\Textures\Emotion\Cry]=]},
        {"{可爱}", [=[Interface\Addons\WanTiny\Textures\Emotion\Cutie]=]},
        {"{鄙视}", [=[Interface\Addons\WanTiny\Textures\Emotion\Despise]=]},
        {"{美梦}", [=[Interface\Addons\WanTiny\Textures\Emotion\Dreamsmile]=]},
        {"{尴尬}", [=[Interface\Addons\WanTiny\Textures\Emotion\Embarrass]=]},
        {"{邪恶}", [=[Interface\Addons\WanTiny\Textures\Emotion\Evil]=]},
        {"{兴奋}", [=[Interface\Addons\WanTiny\Textures\Emotion\Excited]=]},
        {"{晕}", [=[Interface\Addons\WanTiny\Textures\Emotion\Faint]=]},
        {"{打架}", [=[Interface\Addons\WanTiny\Textures\Emotion\Fight]=]},
        {"{流感}", [=[Interface\Addons\WanTiny\Textures\Emotion\Flu]=]},
        {"{呆}", [=[Interface\Addons\WanTiny\Textures\Emotion\Freeze]=]},
        {"{皱眉}", [=[Interface\Addons\WanTiny\Textures\Emotion\Frown]=]},
        {"{致敬}", [=[Interface\Addons\WanTiny\Textures\Emotion\Greet]=]},
        {"{鬼脸}", [=[Interface\Addons\WanTiny\Textures\Emotion\Grimace]=]},
        {"{龇牙}", [=[Interface\Addons\WanTiny\Textures\Emotion\Growl]=]},
        {"{开心}", [=[Interface\Addons\WanTiny\Textures\Emotion\Happy]=]},
        {"{心}", [=[Interface\Addons\WanTiny\Textures\Emotion\Heart]=]},
        {"{恐惧}", [=[Interface\Addons\WanTiny\Textures\Emotion\Horror]=]},
        {"{生病}", [=[Interface\Addons\WanTiny\Textures\Emotion\Ill]=]},
        {"{无辜}", [=[Interface\Addons\WanTiny\Textures\Emotion\Innocent]=]},
        {"{功夫}", [=[Interface\Addons\WanTiny\Textures\Emotion\Kongfu]=]},
        {"{花痴}", [=[Interface\Addons\WanTiny\Textures\Emotion\Love]=]},
        {"{邮件}", [=[Interface\Addons\WanTiny\Textures\Emotion\Mail]=]},
        {"{化妆}", [=[Interface\Addons\WanTiny\Textures\Emotion\Makeup]=]},
        -- {"{马里奥}", [=[Interface\Addons\WanTiny\Textures\Emotion\Mario]=]},
        {"{沉思}", [=[Interface\Addons\WanTiny\Textures\Emotion\Meditate]=]},
        {"{可怜}", [=[Interface\Addons\WanTiny\Textures\Emotion\Miserable]=]},
        {"{好}", [=[Interface\Addons\WanTiny\Textures\Emotion\Okay]=]},
        {"{漂亮}", [=[Interface\Addons\WanTiny\Textures\Emotion\Pretty]=]},
        {"{吐}", [=[Interface\Addons\WanTiny\Textures\Emotion\Puke]=]},
        {"{握手}", [=[Interface\Addons\WanTiny\Textures\Emotion\Shake]=]},
        {"{喊}", [=[Interface\Addons\WanTiny\Textures\Emotion\Shout]=]},
        {"{闭嘴}", [=[Interface\Addons\WanTiny\Textures\Emotion\Shuuuu]=]},
        {"{害羞}", [=[Interface\Addons\WanTiny\Textures\Emotion\Shy]=]},
        {"{睡觉}", [=[Interface\Addons\WanTiny\Textures\Emotion\Sleep]=]},
        {"{微笑}", [=[Interface\Addons\WanTiny\Textures\Emotion\Smile]=]},
        {"{吃惊}", [=[Interface\Addons\WanTiny\Textures\Emotion\Suprise]=]},
        {"{失败}", [=[Interface\Addons\WanTiny\Textures\Emotion\Surrender]=]},
        {"{流汗}", [=[Interface\Addons\WanTiny\Textures\Emotion\Sweat]=]},
        {"{流泪}", [=[Interface\Addons\WanTiny\Textures\Emotion\Tear]=]},
        {"{悲剧}", [=[Interface\Addons\WanTiny\Textures\Emotion\Tears]=]},
        {"{想}", [=[Interface\Addons\WanTiny\Textures\Emotion\Think]=]},
        {"{偷笑}", [=[Interface\Addons\WanTiny\Textures\Emotion\Titter]=]},
        {"{猥琐}", [=[Interface\Addons\WanTiny\Textures\Emotion\Ugly]=]},
        {"{胜利}", [=[Interface\Addons\WanTiny\Textures\Emotion\Victory]=]},
        {"{雷锋}", [=[Interface\Addons\WanTiny\Textures\Emotion\Volunteer]=]},
        {"{委屈}", [=[Interface\Addons\WanTiny\Textures\Emotion\Wronged]=]}
}

local function ChatEmoteFilter(self, event, msg, ...)
    if (_G.WChat_Config and _G.WChat_Config.EnableEmoteInput) then
        for i = customEmoteStartIndex, #emotes do
            if msg:find(emotes[i][1]) then
                msg = msg:gsub(emotes[i][1], format(fmtstring, emotes[i][2]), 1)
            end
        end
    end
    return false, msg, ...
end

local function EmoteIconMouseUp(frame, button)
    if (button == "LeftButton") then
        local chatFrame = GetCVar("chatStyle") == "im" and SELECTED_CHAT_FRAME or DEFAULT_CHAT_FRAME
        local eb = chatFrame and chatFrame.editBox
        if (eb) then
            eb:Insert(frame.text)
            eb:Show();
            eb:SetFocus()
        end
    end
    WChat:ToggleEmoteTable()
end

function WChat:InitEmoteTableFrame()
    fmtstring = format("\124T%%s:%d\124t", max(floor(select(2, SELECTED_CHAT_FRAME:GetFont())), _G.WChat_Config.EmoteIconSize))

    EmoteTableFrame = CreateFrame("Frame", "EmoteTableFrame", UIParent, BackdropTemplateMixin and "BackdropTemplate" or nil)
    EmoteTableFrame:SetMovable(true)
    EmoteTableFrame:RegisterForDrag("LeftButton")
    EmoteTableFrame:SetScript("OnDragStart", EmoteTableFrame.StartMoving)
    EmoteTableFrame:SetScript("OnDragStop", EmoteTableFrame.StopMovingOrSizing)
    EmoteTableFrame:EnableMouse(true)
    EmoteTableFrame:SetWidth((_G.WChat_Config.EmoteIconListSize + 6) * 12 + 10)
    EmoteTableFrame:SetHeight((_G.WChat_Config.EmoteIconListSize + 6) * 5 + 10)
    EmoteTableFrame:SetPoint("BOTTOM", ChatFrame1EditBox, _G.WChat_Config.EmoteOffsetX, _G.WChat_Config.EmoteOffsetY)
    EmoteTableFrame:SetBackdrop({
        bgFile = "Interface\\Buttons\\WHITE8x8",
        edgeFile = "Interface\\Tooltips\\UI-Tooltip-Border",
        tile = true,
        tileSize = 16,
        edgeSize = 16,
        insets = {left = 3, right = 3, top = 3, bottom = 3}
    })
    EmoteTableFrame:SetBackdropColor(0.05, 0.05, 0.05, 0.8)
    EmoteTableFrame:SetBackdropBorderColor(0.3, 0.3, 0.3)
    -- 表情选择框出现位置 默认30,30
    EmoteTableFrame:Hide()
    EmoteTableFrame:SetFrameStrata("DIALOG")
    local icon, row, col
    row = 1
    col = 1
    for i = 1, #emotes do
        text = emotes[i][1]
        texture = emotes[i][2]
        icon = CreateFrame("Frame", format("IconButton%d", i), EmoteTableFrame)
        icon:SetWidth(_G.WChat_Config.EmoteIconListSize + 6)
        icon:SetHeight(_G.WChat_Config.EmoteIconListSize + 6)
        icon.text = text
        icon.texture = icon:CreateTexture(nil, "ARTWORK")
        icon.texture:SetTexture(texture)
        icon.texture:SetAllPoints(icon)
        icon:Show()
        icon:SetPoint(
            "TOPLEFT",
            5 + (col - 1) * (WChat_Config.EmoteIconListSize + 6),
            -5 - (row - 1) * (WChat_Config.EmoteIconListSize + 6)
        )
        icon:SetScript("OnMouseUp", EmoteIconMouseUp)
        icon:EnableMouse(true)
        col = col + 1
        if (col > 12) then
            row = row + 1
            col = 1
        end
    end
    
    ChatFrame_AddMessageEventFilter("CHAT_MSG_CHANNEL", ChatEmoteFilter)-- 公共频道
    ChatFrame_AddMessageEventFilter("CHAT_MSG_SAY", ChatEmoteFilter)-- 说
    ChatFrame_AddMessageEventFilter("CHAT_MSG_YELL", ChatEmoteFilter)-- 大喊
    ChatFrame_AddMessageEventFilter("CHAT_MSG_RAID", ChatEmoteFilter)-- 团队
    ChatFrame_AddMessageEventFilter("CHAT_MSG_RAID_LEADER", ChatEmoteFilter)-- 团队领袖
    ChatFrame_AddMessageEventFilter("CHAT_MSG_PARTY", ChatEmoteFilter)-- 队伍
    ChatFrame_AddMessageEventFilter("CHAT_MSG_PARTY_LEADER", ChatEmoteFilter)-- 队伍领袖
    ChatFrame_AddMessageEventFilter("CHAT_MSG_GUILD", ChatEmoteFilter)-- 公会
    
    ChatFrame_AddMessageEventFilter("CHAT_MSG_AFK", ChatEmoteFilter)-- AFK玩家自动回复
    ChatFrame_AddMessageEventFilter("CHAT_MSG_DND", ChatEmoteFilter)-- 切勿打扰自动回复
    
    -- 副本和副本领袖
    ChatFrame_AddMessageEventFilter("CHAT_MSG_INSTANCE_CHAT", ChatEmoteFilter)
    ChatFrame_AddMessageEventFilter("CHAT_MSG_INSTANCE_CHAT_LEADER", ChatEmoteFilter)
    -- 解析战网私聊
    ChatFrame_AddMessageEventFilter("CHAT_MSG_WHISPER", ChatEmoteFilter)
    ChatFrame_AddMessageEventFilter("CHAT_MSG_WHISPER_INFORM", ChatEmoteFilter)
    ChatFrame_AddMessageEventFilter("CHAT_MSG_BN_WHISPER", ChatEmoteFilter)
    ChatFrame_AddMessageEventFilter("CHAT_MSG_BN_WHISPER_INFORM", ChatEmoteFilter)
    -- 解析社区聊天内容
    ChatFrame_AddMessageEventFilter("CHAT_MSG_COMMUNITIES_CHANNEL", ChatEmoteFilter)
end

function WChat:ToggleEmoteTable()
    if (EmoteTableFrame:IsShown()) then
        EmoteTableFrame:Hide()
    else
        EmoteTableFrame:Show()
    end
end
-----------------------------------频道和密语对象的快速切换

function ChatEdit_CustomTabPressed(...)
    return ChatEdit_CustomTabPressed_Inner(...)
end

local cycles = {
        -- "说"
        {
            chatType = "SAY",
            use = function(self, editbox)
                return 1
            end
        },
        --大喊
        {
            chatType = "YELL",
            use = function(self, editbox)
                return 1
            end
        },
        --小队
        {
            chatType = "PARTY",
            use = function(self, editbox)
                return IsInGroup()
            end
        },
        --团队
        {
            chatType = "RAID",
            use = function(self, editbox)
                return IsInRaid()
            end
        },
        --实时聊天
        {
            chatType = "INSTANCE_CHAT",
            use = function(self, editbox)
                return select(2, IsInInstance()) == "pvp"
            end
        },
        --公会
        {
            chatType = "GUILD",
            use = function(self, editbox)
                return IsInGuild()
            end
        },
        --频道
        {
            chatType = "CHANNEL",
            use = function(self, editbox, currChatType)
                local currNum
                if currChatType ~= "CHANNEL" then
                    currNum = IsShiftKeyDown() and 21 or 0
                else
                    currNum = editbox:GetAttribute("channelTarget")
                end
                local h, r, step = currNum + 1, 20, 1
                if IsShiftKeyDown() then
                    h, r, step = currNum - 1, 1, -1
                end
                for i = h, r, step do
                    local channelNum, channelName = GetChannelName(i)
                    if channelNum > 0 and channelName:find("大脚世界频道") then
                        --print(channelName); --DEBUG
                        editbox:SetAttribute("channelTarget", i)
                        return true
                    end
                end
            end
        },
        {
            chatType = "SAY",
            use = function(self, editbox)
                return 1
            end
        }
}

local chatTypeBeforeSwitch, tellTargetBeforeSwitch --记录在频道和密语之间切换时的状态
function ChatEdit_CustomTabPressed_Inner(self)
    if strsub(tostring(self:GetText()), 1, 1) == "/" then
        return
    end
    local currChatType = self:GetAttribute("chatType")
    if (IsControlKeyDown()) then
        if (currChatType == "WHISPER" or currChatType == "BN_WHISPER") then
            --记录之前的密语对象，以便后续切回
            self:SetAttribute("chatType", chatTypeBeforeSwitch or "SAY")
            ChatEdit_UpdateHeader(self)
            chatTypeBeforeSwitch = "WHISPER"
            tellTargetBeforeSwitch = self:GetAttribute("tellTarget")
            return --这里和下面不同，这里可以不返回true
        else
            local newTarget, newTargetType = ChatEdit_GetNextTellTarget()
            if tellTargetBeforeSwitch or (newTarget and newTarget ~= "") then
                self:SetAttribute("chatType", tellTargetBeforeSwitch and chatTypeBeforeSwitch or newTargetType)
                self:SetAttribute("tellTarget", tellTargetBeforeSwitch or newTarget)
                ChatEdit_UpdateHeader(self)
                chatTypeBeforeSwitch = currChatType
                tellTargetBeforeSwitch = nil
                return true --这里必须返回true，否则会被暴雪默认的再切换一次密语对象
            end
        end
    end
    
    --对于说然后SHIFT的情况，因为没有return，所以第一层循环会一直遍历到最后的SAY
    for i, curr in ipairs(cycles) do
        if curr.chatType == currChatType then
            local h, r, step = i + 1, #cycles, 1
            if IsShiftKeyDown() then
                h, r, step = i - 1, 1, -1
            end
            if currChatType == "CHANNEL" then
                h = i
            end --频道仍然要测试一下
            for j = h, r, step do
                if cycles[j]:use(self, currChatType) then
                    self:SetAttribute("chatType", cycles[j].chatType)
                    ChatEdit_UpdateHeader(self)
                    return
                end
            end
        end
    end
end



-----------------------------点击时间复制 (alaCHAT完整实现)
-- 基于alaCHAT的完整时间戳点击复制功能

local hooksecurefunc = hooksecurefunc;
local date = date;
local format, gsub, strmatch = string.format, string.gsub, string.match;
local GetCVar, SetCVar = GetCVar, SetCVar;
local ChatEdit_ChooseBoxForSend, ChatEdit_ActivateChat = ChatEdit_ChooseBoxForSend, ChatEdit_ActivateChat;
local ItemRefTooltip = ItemRefTooltip;
local _G = _G;

local __copy = {};
local _db = {};

local __TAG = "";
local __FMT = "";
local __CLR = { 1.0, 1.0, 1.0, };

-- 设置时间戳 (alaCHAT原版)
local function SetTimeStamp()
    __TAG = format("|cff%.2x%.2x%.2x|Haccopy:-1|h%s|h|r", __CLR[1] * 255, __CLR[2] * 255, __CLR[3] * 255, (__FMT == nil or __FMT == "" or __FMT == "none") and "*" or __FMT);
    if GetCVar("showTimestamps") ~= __TAG then
        SetCVar("showTimestamps", __TAG);
        _G.CHAT_TIMESTAMP_FORMAT = __TAG;
    end
end

-- 初始化时间戳点击复制功能 (alaCHAT原版)
local B_Initialized = false;
local function Init()
    B_Initialized = true;
    local _ItemRefTooltip_SetHyperlink = ItemRefTooltip.SetHyperlink;
    function ItemRefTooltip:SetHyperlink(link, ...)
        if link == "accopy:-1" then
            local focus = GetMouseFocus and GetMouseFocus() or GetMouseFoci and GetMouseFoci()[1];
            if not focus:IsObjectType("FontString") then
                focus = focus:GetParent();
                if not focus:IsObjectType("FontString") then
                    return;
                end
            end
            local tx = focus:GetText();
            if tx == nil or tx == "" then
                return;
            end
            -- 清理文本格式 (alaCHAT原版处理)
            tx = gsub(tx, "|H.-|h", "");
            tx = gsub(tx, "|c%x%x%x%x%x%x%x%x", "");
            tx = gsub(tx, "|[Hhr]", "");
            local editBox = ChatEdit_ChooseBoxForSend();
            if not editBox:HasFocus() then
                ChatEdit_ActivateChat(editBox);
            end
            editBox:SetText(tx);
            return;
        end
        return _ItemRefTooltip_SetHyperlink(self, link, ...);
    end
    -- 监听时间戳设置变化
    if InterfaceOptionsSocialPanelTimestamps ~= nil and InterfaceOptionsSocialPanelTimestamps.SetValue ~= nil then
        hooksecurefunc(InterfaceOptionsSocialPanelTimestamps, "SetValue", function(self, value)
            _db.toggle = false;
        end);
    end
end

-- alaCHAT模块功能实现
function __copy.color(value, loading)
    if not loading and _db.toggle then
        if value ~= nil and( __CLR[1] ~= value[1] or __CLR[2] ~= value[2] or __CLR[3] ~= value[3]) then
            __CLR = { value[1], value[2], value[3], };
            SetTimeStamp();
        end
    end
end

function __copy.format(value, loading)
    if not loading and _db.toggle then
        if value ~= nil and __FMT ~= value then
            __FMT = value;
            SetTimeStamp();
        end
    end
end

function __copy.toggle(value, loading)
    if value then
        if not B_Initialized then
            Init();
        end
        local c = _db.color;
        local f = _db.format;
        __FMT = f;
        __CLR = { c[1], c[2], c[3], };
        SetTimeStamp();
    elseif loading then
        local fmt = GetCVar("showTimestamps");
        if fmt ~= "none" then
            local fmt2 = strmatch(fmt, "|h(.+)|h");
            if fmt2 ~= nil then
                SetCVar("showTimestamps", fmt2);
                _G.CHAT_TIMESTAMP_FORMAT = fmt2;
            end
        end
    else
        if __FMT == "none" or __FMT == "" or __FMT == nil then
            if GetCVar("showTimestamps") ~= "none" then
                SetCVar("showTimestamps", "none");
                _G.CHAT_TIMESTAMP_FORMAT = nil;
            end
        else
            if GetCVar("showTimestamps") ~= __FMT then
                SetCVar("showTimestamps", __FMT);
                _G.CHAT_TIMESTAMP_FORMAT = __FMT;
            end
        end
    end
end

-- 存储原始时间戳格式
local originalTimestampFormat = nil

-- WChat集成的时间戳复制功能
function WChat:InitTimestampCopy()
    if _G.WChat_Config.EnableTimestampCopy then
        -- 保存当前的时间戳格式作为原始格式（如果还没有保存的话）
        if originalTimestampFormat == nil then
            local currentFormat = GetCVar("showTimestamps");
            if currentFormat and not currentFormat:find("|Haccopy:") then
                -- 当前格式不包含复制链接，保存为原始格式
                originalTimestampFormat = currentFormat;
            else
                -- 当前格式包含复制链接，尝试提取原始格式
                if currentFormat then
                    local extracted = currentFormat:match("|h(.+)|h");
                    originalTimestampFormat = (extracted and extracted ~= "*") and extracted or "none";
                else
                    originalTimestampFormat = "none";
                end
            end
        end

        -- 初始化_db配置
        _db = {
            toggle = true,
            format = _G.WChat_Config.TimestampFormat or "%H:%M:%S",
            color = _G.WChat_Config.TimestampColor or { 1.0, 1.0, 1.0 }
        };

        -- 启用功能
        __copy.toggle(true, false);
    else
        -- 禁用功能时，完全隐藏时间戳
        if _db then
            -- 设置__FMT为"none"，这样toggle(false)会隐藏时间戳
            __FMT = "none";
            __copy.toggle(false, false);
            _db = nil;
        end

        -- 清理全局变量，避免污染
        if B_Initialized then
            -- 恢复原始的ItemRefTooltip.SetHyperlink
            if _ItemRefTooltip_SetHyperlink then
                ItemRefTooltip.SetHyperlink = _ItemRefTooltip_SetHyperlink;
                _ItemRefTooltip_SetHyperlink = nil;
            end
            B_Initialized = false;
        end

        -- 确保时间戳被完全隐藏
        if GetCVar("showTimestamps") ~= "none" then
            SetCVar("showTimestamps", "none");
            _G.CHAT_TIMESTAMP_FORMAT = nil;
        end
    end
end

-----------------------------聊天条

--[[=========================== 变量区 ==========================]]
-- 是否可移动的标记
local IsMovable = false -- 没事干别动这个，你改成ture那么进入游戏后聊天条就是可以移动的
--[[=============================== END ==============================]]
local chatFrame = SELECTED_DOCK_FRAME -- 聊天框架
local inputbox = chatFrame.editBox -- 输入框

COLORSCHEME_BORDER = {0.3, 0.3, 0.3, 1}
-- 边框颜色
-- 主框架初始化
local ChatBar = CreateFrame("Frame", nil, UIParent, BackdropTemplateMixin and "BackdropTemplate" or nil)
WChatBar = ChatBar

local function ChannelSay_OnClick()
    ChatFrame_OpenChat("/s " .. inputbox:GetText(), chatFrame)
end

local function ChannelYell_OnClick()
    ChatFrame_OpenChat("/y " .. inputbox:GetText(), chatFrame)
end

local function ChannelParty_OnClick()
    ChatFrame_OpenChat("/p " .. inputbox:GetText(), chatFrame)
end

local function ChannelGuild_OnClick()
    ChatFrame_OpenChat("/g " .. inputbox:GetText(), chatFrame)
end

local function ChannelRaid_OnClick()
    ChatFrame_OpenChat("/raid " .. inputbox:GetText(), chatFrame)
end

-- 综合频道点击处理
local function ChannelGen_OnClick(self, button)
    if button == "RightButton" then
        -- 右键：切换频道显示/隐藏
        ToggleChannelShowHide("综合")
        C_Timer.After(0.1, UpdateChannelXIcons)
    else
        -- 左键：如果频道未显示则先显示，然后发言
        if not IsChannelShown("综合") then
            ToggleChannelShowHide("综合")
            C_Timer.After(0.1, UpdateChannelXIcons)
        end
        local channel, _, _ = GetChannelName("综合")
        if channel then
            ChatFrame_OpenChat("/" .. channel .. " " .. inputbox:GetText(), chatFrame)
        end
    end
end

-- 交易频道点击处理
local function ChannelTrade_OnClick(self, button)
    if button == "RightButton" then
        -- 右键：切换频道显示/隐藏
        ToggleChannelShowHide("交易")
        C_Timer.After(0.1, UpdateChannelXIcons)
    else
        -- 左键：如果频道未显示则先显示，然后发言
        if not IsChannelShown("交易") then
            ToggleChannelShowHide("交易")
            C_Timer.After(0.1, UpdateChannelXIcons)
        end
        local channel, _, _ = GetChannelName("交易")
        if channel then
            ChatFrame_OpenChat("/" .. channel .. " " .. inputbox:GetText(), chatFrame)
        end
    end
end

-- 寻求组队频道点击处理
local function ChannelLFG_OnClick(self, button)
    if button == "RightButton" then
        -- 右键：切换频道显示/隐藏
        ToggleChannelShowHide("寻求组队")
        C_Timer.After(0.1, UpdateChannelXIcons)
    else
        -- 左键：如果频道未显示则先显示，然后发言
        if not IsChannelShown("寻求组队") then
            ToggleChannelShowHide("寻求组队")
            C_Timer.After(0.1, UpdateChannelXIcons)
        end
        local channel, _, _ = GetChannelName("寻求组队")
        if channel then
            ChatFrame_OpenChat("/" .. channel .. " " .. inputbox:GetText(), chatFrame)
        end
    end
end

local function ChannelBG_OnClick(self, button)
    -- 副本频道按钮，只有左键功能
    ChatFrame_OpenChat("/bg " .. inputbox:GetText(), chatFrame)
end

-- function Channel01_OnClick()
--     ChatFrame_OpenChat("/1 ", chatFrame)
-- end
local function ChatEmote_OnClick()
    WChat:ToggleEmoteTable()
end

local function ChannelWorld_OnClick(self, button)
    if button == "RightButton" then
        -- 右键：切换频道显示/隐藏
        ToggleChannelShowHide("大脚世界频道")
        C_Timer.After(0.1, UpdateChannelXIcons)
    else
        -- 左键：发言或加入频道后发言
        local _, channelName, _ = GetChannelName("大脚世界频道")
        if channelName == nil then
            JoinPermanentChannel("大脚世界频道", nil, 1, 1)
            ChatFrame_RemoveMessageGroup(chatFrame, "CHANNEL")
            ChatFrame_AddChannel(chatFrame, "大脚世界频道")
            print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r |cff00d200已加入大脚世界频道|r")
            -- 确保频道显示
            C_Timer.After(0.2, function()
                if not IsChannelShown("大脚世界频道") then
                    ChatFrame_AddChannel(DEFAULT_CHAT_FRAME, "大脚世界频道")
                    _G.WChat_Config.HiddenChannels["大脚世界频道"] = nil
                end
                UpdateChannelXIcons()
            end)
        else
            -- 如果频道未显示则先显示
            if not IsChannelShown("大脚世界频道") then
                ToggleChannelShowHide("大脚世界频道")
                C_Timer.After(0.1, UpdateChannelXIcons)
            end
        end

        local channel, _, _ = GetChannelName("大脚世界频道")
        if channel then
            ChatFrame_OpenChat("/" .. channel .. " " .. inputbox:GetText(), chatFrame)
        end
    end
end

local function Roll_OnClick()
    RandomRoll(1, 100)
end

local function Report_OnClick(self, button)
    local statText = WChat:StatReport()
    if button == "RightButton" then
        print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r |cff00d200我的属性：|r" .. statText)
    else
        local editBox = ChatEdit_ChooseBoxForSend()
        if not editBox:HasFocus() then
            ChatEdit_ActivateChat(editBox)
        end
        editBox:Insert(statText)
    end
end

local function ChatCopy_OnClick()
    WChat:CopyFunc()
end

-- 插件配置表
local AddonConfigs = {
    BiaoGe = {
        addonName = "BiaoGe",
        displayName = "BiaoGe金团",
        globalVar = "BG",
        mainFrameKey = "MainFrame",
        slashCmd = nil,
        tooltip = function()
            return "|cff00ffff鼠标左键|r-|cffff80ff打开BiaoGe金团|r"
        end
    },
    BiaoGeAI = {
        addonName = "BiaoGeAI",
        displayName = "BiaoGeAI",
        globalVar = "BGAI",
        mainFrameKey = "MainFrame",
        slashCmd = nil,
        tooltip = function()
            return "|cff00ffff鼠标左键|r-|cffff80ff打开BiaoGeAI|r"
        end
    },
    AtlasLootClassic = {
        addonName = "AtlasLootClassic",
        displayName = "Atlas掉落",
        globalVar = nil,
        mainFrameKey = nil,
        slashCmd = "ATLASLOOT",
        tooltip = function()
            return "|cff00ffff鼠标左键|r-|cffff80ff打开Atlas掉落查询|r"
        end
    },
    MeetingHorn = {
        addonName = "MeetingHorn",
        displayName = "MeetingHorn集合石",
        globalVar = nil,
        mainFrameKey = nil,
        slashCmd = "MEETINGHORN",
        tooltip = function()
            local tooltip = "|cff00ffff鼠标左键|r-|cffff80ff打开集结号|r"

            local addon = LibStub and LibStub('AceAddon-3.0'):GetAddon('MeetingHorn', true)
            if not addon then return tooltip end

            local lfg = addon:GetModule('LFG', true)
            if not lfg then return tooltip end

            local icon1 = "|TInterface\\AddOns\\MeetingHorn\\Media\\DataBroker:16:16:0:0:64:32:0:32:0:32|t"
            local icon2 = "|TInterface\\AddOns\\MeetingHorn\\Media\\DataBroker:16:16:0:0:64:32:32:64:0:32|t"
            tooltip = tooltip .. "\n" .. icon2 .. "|cffFFD700活动数量: " .. lfg:GetActivityCount() .. "|r"

            local count = lfg:GetCurrentActivity() and lfg:GetApplicantCount() or lfg:GetApplicationCount()
            local label = lfg:GetCurrentActivity() and "申请者数量" or "申请数量"
            return tooltip .. "\n" .. icon1 .. "|cffFFD700" .. label .. ": " .. count .. "|r"
        end
    }
}

-- 通用插件按钮点击处理函数
local function CreateAddonClickHandler(configKey)
    return function()
        local config = AddonConfigs[configKey]
        if not config then return end

        -- 检查并加载插件
        if not IsAddOnLoaded(config.addonName) then
            local loaded, reason = LoadAddOn(config.addonName)
            if not loaded then
                print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r |cffff0000" .. config.displayName .. "插件加载失败: " .. (reason or "未知原因") .. "|r")
                return
            end
            -- 刷新聊天条按钮
            WChat:RefreshChatBarButtons()
        end

        -- 尝试打开插件界面
        if config.globalVar and config.mainFrameKey then
            -- 使用MainFrame方式
            local addon = _G[config.globalVar]
            if addon and addon[config.mainFrameKey] then
                addon[config.mainFrameKey]:SetShown(not addon[config.mainFrameKey]:IsVisible())
            end
        elseif config.slashCmd then
            -- 使用斜杠命令方式
            if configKey == "MeetingHorn" then
                -- MeetingHorn特殊处理
                local meetingHornAddon = LibStub and LibStub('AceAddon-3.0'):GetAddon('MeetingHorn', true)
                if meetingHornAddon and meetingHornAddon.Toggle then
                    meetingHornAddon:Toggle()
                elseif SlashCmdList[config.slashCmd] then
                    SlashCmdList[config.slashCmd]("")
                end
            else
                -- 其他插件使用斜杠命令
                if SlashCmdList[config.slashCmd] then
                    SlashCmdList[config.slashCmd]("")
                end
            end
        end
    end
end

-- 创建具体的点击处理函数
local Gold_OnClick = CreateAddonClickHandler("BiaoGe")
local AI_OnClick = CreateAddonClickHandler("BiaoGeAI")
local Atlas_OnClick = CreateAddonClickHandler("AtlasLootClassic")
local MeetingHorn_OnClick = CreateAddonClickHandler("MeetingHorn")

local function Movelock_OnClick(self, button)
    if button == "LeftButton" then
        if IsMovable then
            print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r |cffd20000锁定聊天条|r")
            IsMovable = false
            WChatBar:SetBackdrop(nil)

            local point, relativeTo, relativePoint, xOfs, yOfs = WChatBar:GetPoint()

            if relativeTo then
                WChat_Config.Position = {point = point, relativeTo = relativeTo:GetName(), relativePoint = relativePoint, xOfs = xOfs, yOfs = yOfs}
            else
                WChat_Config.Position = {point = point, relativeTo = nil, relativePoint = relativePoint, xOfs = xOfs, yOfs = yOfs}
            end
        else
            print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r |cff00d200解锁聊天条|r")
            IsMovable = true
            WChatBar:SetBackdrop(
                {
                    bgFile = "Interface/DialogFrame/UI-DialogBox-Background",
                    edgeFile = "Interface/DialogFrame/UI-DialogBox-Border",
                    tile = true,
                    tileSize = 16,
                    edgeSize = 16,
                    insets = {left = 4, right = 4, top = 4, bottom = 4}
                }
        )
        end
        WChatBar:EnableMouse(IsMovable)
    elseif button == "MiddleButton" then
        if IsMovable == false then
            return
        end
        WChatBar:ClearAllPoints()
        if WChat_Config.UseVertical then
            WChatBar:SetPoint("TOPLEFT", "ChatFrame1", "TOPRIGHT", WChat_Config.ChatBarOffsetX + 30, WChat_Config.ChatBarOffsetY + 25)
        else
            WChatBar:SetPoint("TOPLEFT", "ChatFrame1", "BOTTOMLEFT", WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY - 30)
        end
        -- 清除自定义位置，使用默认位置
        WChat_Config.Position = nil
    end
end

local ChannelButtons = {
    {name = "say", text = "说", color = {1.00, 1.00, 1.00}, callback = ChannelSay_OnClick, tooltip = function() return "|cff00ffff鼠标左键|r-|cffff80ff切换到说话频道|r" end},
    {name = "yell", text = "喊", color = {1.00, 0.25, 0.25}, callback = ChannelYell_OnClick, tooltip = function() return "|cff00ffff鼠标左键|r-|cffff80ff切换到大喊频道|r" end},
    {name = "party", text = "队", color = {0.66, 0.66, 1.00}, callback = ChannelParty_OnClick, tooltip = function() return "|cff00ffff鼠标左键|r-|cffff80ff切换到队伍频道|r" end},
    {name = "guild", text = "会", color = {0.25, 1.00, 0.25}, callback = ChannelGuild_OnClick, tooltip = function() return "|cff00ffff鼠标左键|r-|cffff80ff切换到公会频道|r" end},
    {name = "raid", text = "团", color = {1.00, 0.50, 0.00}, callback = ChannelRaid_OnClick, tooltip = function() return "|cff00ffff鼠标左键|r-|cffff80ff切换到团队频道|r" end},
    {name = "LFT", text = "副", color = {1.00, 0.50, 0.00}, callback = ChannelBG_OnClick, tooltip = function() return "|cff00ffff鼠标左键|r-|cffff80ff切换到副本频道|r" end},
    {name = "chnGen", text = "综", color = {0.82, 0.70, 0.55}, callback = ChannelGen_OnClick, tooltip = function() return "|cff00ffff鼠标左键|r-|cffff80ff切换到综合频道|r\n|cff00ffff鼠标右键|r-|cffff80ff切换频道显示/隐藏|r" end},
    {name = "chnTrade", text = "交", color = {1.00, 0.82, 0.00}, callback = ChannelTrade_OnClick, tooltip = function() return "|cff00ffff鼠标左键|r-|cffff80ff切换到交易频道|r\n|cff00ffff鼠标右键|r-|cffff80ff切换频道显示/隐藏|r" end},
    {name = "chnLFG", text = "组", color = {0.50, 1.00, 0.50}, callback = ChannelLFG_OnClick, tooltip = function() return "|cff00ffff鼠标左键|r-|cffff80ff切换到寻求组队频道|r\n|cff00ffff鼠标右键|r-|cffff80ff切换频道显示/隐藏|r" end},
    {name = "world", text = "世", color = {0.78, 1.00, 0.59}, callback = ChannelWorld_OnClick, tooltip = function() return "|cff00ffff鼠标左键|r-|cffff80ff切换到世界频道|r\n|cff00ffff鼠标右键|r-|cffff80ff切换频道显示/隐藏|r" end},
    {name = "emote", text = "表", color = {1.00, 0.50, 1.00}, callback = ChatEmote_OnClick, tooltip = function() return "|cff00ffff鼠标左键|r-|cffff80ff打开表情选择器|r" end},
    {name = "roll", text = "骰", color = {1.00, 1.00, 0.00}, callback = Roll_OnClick, tooltip = function() return "|cff00ffff鼠标左键|r-|cffff80ff投掷骰子|r" end},
    {name = "report", text = "报", color = {0.80, 0.30, 0.30}, callback = Report_OnClick, tooltip = function() return "|cff00ffff鼠标左键|r-|cffff80ff发送属性报告|r\n|cff00ffff鼠标右键|r-|cffff80ff在聊天框显示属性|r" end},
    {name = "movelock", text = "锁", color = {0.20, 0.20, 0.80}, callback = Movelock_OnClick, tooltip = function() return "|cff00ffff鼠标左键|r-|cffff80ff锁定/解锁聊天条位置|r" end},
    {name = "chatcopy", text = "复", color = {0.20, 0.60, 0.80}, callback = ChatCopy_OnClick, tooltip = function() return "|cff00ffff鼠标左键|r-|cffff80ff复制聊天内容|r" end},
    -- 插件按钮
    {name = "biaoge", text = "金", color = {1.00, 0.84, 0.00}, callback = Gold_OnClick, tooltip = AddonConfigs.BiaoGe.tooltip},
    {name = "biaogeai", text = "AI", color = {0.00, 0.80, 1.00}, callback = AI_OnClick, tooltip = AddonConfigs.BiaoGeAI.tooltip},
    {name = "atlas", text = "掉", color = {0.80, 0.20, 0.80}, callback = Atlas_OnClick, tooltip = AddonConfigs.AtlasLootClassic.tooltip},
    {name = "meetinghorn", text = "集", color = {0.20, 0.80, 0.20}, callback = MeetingHorn_OnClick, tooltip = AddonConfigs.MeetingHorn.tooltip}
}

local function CreateChannelButton(data, index)
    local frame = CreateFrame("Button", "frameName", WChatBar)
    frame:SetWidth(22)
    -- 按钮宽度
    frame:SetHeight(22)
    -- 按钮高度
    frame:SetAlpha(WChat_Config.AlphaOnLeave)
    
    frame:SetFrameLevel(1)
    
    frame:SetScript(
        "OnEnter",
        function(self)
            self:SetAlpha(WChat_Config.AlphaOnEnter)
            -- 显示鼠标提示
            if data.tooltip then
                GameTooltip:SetOwner(self, "ANCHOR_TOP")
                GameTooltip:SetText(data.tooltip(), nil, nil, nil, nil, true)
                GameTooltip:Show()
            end
        end
    )
    frame:SetScript(
        "OnLeave",
        function(self)
            self:SetAlpha(WChat_Config.AlphaOnLeave)
            -- 隐藏鼠标提示
            GameTooltip:Hide()
        end
    )
    if WChat_Config.UseVertical then
        -- 垂直布局：始终以第一个按钮为基准，根据间距正负值向不同方向排列
        frame:SetPoint("TOP", WChatBar, "TOP", 0, (1 - index) * WChat_Config.DistanceVertical)
    else
        frame:SetPoint("LEFT", WChatBar, "LEFT", 10 + (index - 1) * WChat_Config.DistanceHorizontal, 0)
    end
    
    frame:RegisterForClicks("AnyUp")
    frame:SetScript("OnClick", data.callback)
    -- 显示的文字
    frameText = frame:CreateFontString(data.name .. "Text", "OVERLAY")
    -- 字体设置
    frameText:SetFont(STANDARD_TEXT_FONT, 15, "OUTLINE")
    
    frameText:SetJustifyH("CENTER")
    frameText:SetWidth(26)
    frameText:SetHeight(26)
    frameText:SetText(data.text)
    frameText:SetPoint("CENTER", 0, 0)
    
    -- 文字按钮的颜色
    frameText:SetTextColor(data.color[1], data.color[2], data.color[3])

    -- 创建频道屏蔽X图标
    if CHANNEL_CONFIG.MAPPINGS[data.name] then
        -- 创建文字X图标（更可靠）
        frame.X = frame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
        frame.X:SetText("X")
        frame.X:SetTextColor(1, 0, 0, 0.8) -- 红色X
        frame.X:SetFont("Fonts\\FRIZQT__.TTF", 16, "OUTLINE")
        frame.X:SetPoint("CENTER", frame, "CENTER", 0, 0)
        frame.X:Hide() -- 默认隐藏
        print("创建文字X图标: " .. data.name)
    end

    -- 设置按钮名称用于识别
    frame.buttonName = data.name
end

function WChat:InitChatBar()

    WChatBar:SetFrameLevel(0)

    -- 使用竖直布局
    if WChat_Config.UseVertical then
        -- 主框体宽度
        WChatBar:SetWidth(30)
        -- 主框体高度：计算按钮排列的实际范围
        local numButtons = #ChannelButtons
        if numButtons > 0 then
            local buttonRange = math.abs((numButtons - 1) * WChat_Config.DistanceVertical)
            local totalHeight = buttonRange + 30  -- 按钮范围 + 边距
            WChatBar:SetHeight(totalHeight)
        else
            WChatBar:SetHeight(30)
        end
    else
        -- 主框体宽度
        WChatBar:SetWidth(#ChannelButtons * WChat_Config.DistanceHorizontal + 10)
        -- 主框体高度
        WChatBar:SetHeight(30)
    end

    -- 上方聊天输入框
    if WChat_Config.UseTopInput then
        inputbox:ClearAllPoints()
        inputbox:SetPoint("BOTTOMLEFT", chatFrame, "TOPLEFT", 0, 20)
        inputbox:SetPoint("BOTTOMRIGHT", chatFrame, "TOPRIGHT", 0, 20)
    end
    
    -- 位置设定
    if WChat_Config.Position == nil then
        if WChat_Config.UseVertical then
            WChatBar:SetPoint("TOPLEFT", "ChatFrame1", "TOPRIGHT", WChat_Config.ChatBarOffsetX + 30, WChat_Config.ChatBarOffsetY + 25)
        else
            WChatBar:SetPoint("TOPLEFT", "ChatFrame1", "BOTTOMLEFT", WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY - 30)
        end
    else
        local point = WChat_Config.Position.point
        local relativeTo = WChat_Config.Position.relativeTo
        local relativePoint = WChat_Config.Position.relativePoint
        local xOfs = WChat_Config.Position.xOfs
        local yOfs = WChat_Config.Position.yOfs
        WChatBar:SetPoint(point, relativeTo, relativePoint, xOfs, yOfs)
    end

    WChatBar:SetMovable(true)
    WChatBar:RegisterForDrag("LeftButton")
    WChatBar:SetScript("OnDragStart", WChatBar.StartMoving)
    WChatBar:SetScript("OnDragStop", WChatBar.StopMovingOrSizing)
    
    -- 动态创建按钮，根据配置和插件存在性
    local buttonIndex = 1
    for i = 1, #ChannelButtons do
        local buttonData = ChannelButtons[i]
        local shouldShow = true

        -- 检查插件按钮是否应该显示（必须已加载才显示）
        if buttonData.name == "biaoge" then
            shouldShow = WChat_Config.ShowBiaoGeButton and IsAddOnLoaded("BiaoGe")
        elseif buttonData.name == "biaogeai" then
            shouldShow = WChat_Config.ShowBiaoGeAIButton and IsAddOnLoaded("BiaoGeAI")
        elseif buttonData.name == "atlas" then
            shouldShow = WChat_Config.ShowAtlasButton and IsAddOnLoaded("AtlasLootClassic")
        elseif buttonData.name == "meetinghorn" then
            shouldShow = WChat_Config.ShowMeetingHornButton and IsAddOnLoaded("MeetingHorn")
        end

        if shouldShow then
            CreateChannelButton(buttonData, buttonIndex)
            buttonIndex = buttonIndex + 1
        end
    end

    -- 延迟更新频道X图标状态
    C_Timer.After(0.5, UpdateChannelXIcons)

    print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r 聊天条加载完毕")
end

-- 添加StatReport函数
function WChat:StatReport()
    -- 属性报告功能 (参考alaChat实现)
    local function GetItemLevel()
        local slots = { 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15 }
        local playerClass = UnitClassBase('player')
        if playerClass ~= "DRUID" and playerClass ~= "PALADIN" and playerClass ~= "SHAMAN" then
            slots[#slots + 1] = 18 -- 远程武器槽
        end
        slots[#slots + 1] = 16 -- 主手
        slots[#slots + 1] = 17 -- 副手

        local total = 0
        local num = 0
        for index = 1, #slots do
            local slot = slots[index]
            local item = GetInventoryItemLink('player', slot)
            if item ~= nil and item ~= "" then
                local _, _, _, level, _, _, _, _, loc = GetItemInfo(item)
                if level ~= nil then
                    total = total + level
                    num = num + 1
                end
                if slot == 16 and loc == "INVTYPE_2HWEAPON" then
                    break -- 双手武器不计算副手
                end
            end
        end
        if num == 0 then return nil end
        local lvl = total / num + 0.05
        return math.floor(lvl * 10) / 10
    end

    local function GetTalentDesc()
        local _, n1, _, _, p1 = GetTalentTabInfo(1)
        local _, n2, _, _, p2 = GetTalentTabInfo(2)
        local _, n3, _, _, p3 = GetTalentTabInfo(3)
        if p1 == p2 or p2 == p3 or p1 == p3 then
            return "天赋 (" .. p1 .. "/" .. p2 .. "/" .. p3 .. ")"
        elseif p1 > p2 and p1 > p3 then
            return "天赋 " .. n1 .. "(" .. p1 .. "/" .. p2 .. "/" .. p3 .. ")"
        elseif p2 > p1 and p2 > p3 then
            return "天赋 " .. n2 .. "(" .. p1 .. "/" .. p2 .. "/" .. p3 .. ")"
        else
            return "天赋 " .. n3 .. "(" .. p1 .. "/" .. p2 .. "/" .. p3 .. ")"
        end
    end

    local function GetStatReport()
        local ilv = GetItemLevel()
        local class, file = UnitClass('player')
        local health = UnitHealthMax('player')
        local mana = UnitPowerMax('player', 0)

        -- 基础信息
        local report = class .. ", " .. "等级" .. UnitLevel('player') .. ", " .. GetTalentDesc()
        if ilv then
            report = report .. ", 装等 " .. ilv
        end
        report = report .. ", 生命 " .. health
        if mana > 0 then
            report = report .. ", 法力 " .. mana
        end

        -- 根据职业添加特定属性
        if file == "WARRIOR" then
            local _, _, _, _, p3 = GetTalentTabInfo(3)
            local _, _, _, _, p1 = GetTalentTabInfo(1)
            local _, _, _, _, p2 = GetTalentTabInfo(2)
            if p3 >= p1 and p3 >= p2 then
                -- 防护战士
                local _, armor = UnitArmor('player')
                local pChance = GetParryChance()
                local dChance = GetDodgeChance()
                local bChance = GetBlockChance()
                local block = GetShieldBlock()
                report = report .. ", 护甲 " .. armor
                report = report .. ", 招架 " .. string.format("%.1f", pChance) .. "%"
                report = report .. ", 躲闪 " .. string.format("%.1f", dChance) .. "%"
                report = report .. ", 格挡 " .. string.format("%.1f", bChance) .. "% (" .. block .. ")"
            else
                -- 输出战士
                local apBase, apPos, apNeg = UnitAttackPower('player')
                local critChance = GetCritChance()
                report = report .. ", 攻强 " .. (apBase + apPos + apNeg)
                report = report .. ", 暴击 " .. string.format("%.1f", critChance) .. "%"
            end
        elseif file == "HUNTER" then
            local rapBase, rapPos, rapNeg = UnitRangedAttackPower('player')
            local critChance = GetRangedCritChance()
            report = report .. ", 远攻 " .. (rapBase + rapPos + rapNeg)
            report = report .. ", 暴击 " .. string.format("%.1f", critChance) .. "%"
        elseif file == "MAGE" or file == "WARLOCK" then
            local sp = GetSpellBonusDamage(2)
            local critChance = GetSpellCritChance(2)
            report = report .. ", 法强 " .. sp
            report = report .. ", 暴击 " .. string.format("%.1f", critChance) .. "%"
        elseif file == "PRIEST" then
            local _, _, _, _, p3 = GetTalentTabInfo(3)
            local _, _, _, _, p1 = GetTalentTabInfo(1)
            local _, _, _, _, p2 = GetTalentTabInfo(2)
            if p3 >= p1 and p3 >= p2 then
                -- 暗牧
                local sp = GetSpellBonusDamage(2)
                local critChance = GetSpellCritChance(2)
                report = report .. ", 法强 " .. sp
                report = report .. ", 暴击 " .. string.format("%.1f", critChance) .. "%"
            else
                -- 治疗牧师
                local heal = GetSpellBonusHealing()
                local critChance = GetSpellCritChance(2)
                report = report .. ", 治疗 " .. heal
                report = report .. ", 暴击 " .. string.format("%.1f", critChance) .. "%"
            end
        elseif file == "PALADIN" then
            local _, _, _, _, p1 = GetTalentTabInfo(1)
            local _, _, _, _, p2 = GetTalentTabInfo(2)
            local _, _, _, _, p3 = GetTalentTabInfo(3)
            if p1 >= p2 and p1 >= p3 then
                -- 神圣骑士
                local heal = GetSpellBonusHealing()
                local critChance = GetSpellCritChance(2)
                report = report .. ", 治疗 " .. heal
                report = report .. ", 暴击 " .. string.format("%.1f", critChance) .. "%"
            elseif p2 >= p1 and p2 >= p3 then
                -- 防护骑士
                local _, armor = UnitArmor('player')
                local pChance = GetParryChance()
                local dChance = GetDodgeChance()
                local bChance = GetBlockChance()
                local block = GetShieldBlock()
                report = report .. ", 护甲 " .. armor
                report = report .. ", 招架 " .. string.format("%.1f", pChance) .. "%"
                report = report .. ", 躲闪 " .. string.format("%.1f", dChance) .. "%"
                report = report .. ", 格挡 " .. string.format("%.1f", bChance) .. "% (" .. block .. ")"
            else
                -- 惩戒骑士
                local apBase, apPos, apNeg = UnitAttackPower('player')
                local critChance = GetCritChance()
                report = report .. ", 攻强 " .. (apBase + apPos + apNeg)
                report = report .. ", 暴击 " .. string.format("%.1f", critChance) .. "%"
            end
        elseif file == "ROGUE" then
            local apBase, apPos, apNeg = UnitAttackPower('player')
            local critChance = GetCritChance()
            report = report .. ", 攻强 " .. (apBase + apPos + apNeg)
            report = report .. ", 暴击 " .. string.format("%.1f", critChance) .. "%"
        elseif file == "DRUID" then
            local _, _, _, _, p1 = GetTalentTabInfo(1)
            local _, _, _, _, p2 = GetTalentTabInfo(2)
            local _, _, _, _, p3 = GetTalentTabInfo(3)
            if p1 > p2 and p1 >= p3 then
                -- 平衡德鲁伊
                local sp = GetSpellBonusDamage(4) -- 自然法术
                local critChance = GetSpellCritChance(4)
                report = report .. ", 法强 " .. sp
                report = report .. ", 暴击 " .. string.format("%.1f", critChance) .. "%"
            elseif p3 >= p1 and p3 >= p2 then
                -- 恢复德鲁伊
                local heal = GetSpellBonusHealing()
                local critChance = GetSpellCritChance(4)
                report = report .. ", 治疗 " .. heal
                report = report .. ", 暴击 " .. string.format("%.1f", critChance) .. "%"
            else
                -- 野性德鲁伊 (需要根据形态判断)
                local apBase, apPos, apNeg = UnitAttackPower('player')
                local critChance = GetCritChance()
                report = report .. ", 攻强 " .. (apBase + apPos + apNeg)
                report = report .. ", 暴击 " .. string.format("%.1f", critChance) .. "%"
            end
        elseif file == "SHAMAN" then
            local _, _, _, _, p1 = GetTalentTabInfo(1)
            local _, _, _, _, p2 = GetTalentTabInfo(2)
            local _, _, _, _, p3 = GetTalentTabInfo(3)
            if p1 > p2 and p1 >= p3 then
                -- 元素萨满
                local sp = GetSpellBonusDamage(4) -- 自然法术
                local critChance = GetSpellCritChance(4)
                report = report .. ", 法强 " .. sp
                report = report .. ", 暴击 " .. string.format("%.1f", critChance) .. "%"
            elseif p2 >= p1 and p2 >= p3 then
                -- 增强萨满
                local apBase, apPos, apNeg = UnitAttackPower('player')
                local critChance = GetCritChance()
                report = report .. ", 攻强 " .. (apBase + apPos + apNeg)
                report = report .. ", 暴击 " .. string.format("%.1f", critChance) .. "%"
            else
                -- 恢复萨满
                local heal = GetSpellBonusHealing()
                local critChance = GetSpellCritChance(4)
                report = report .. ", 治疗 " .. heal
                report = report .. ", 暴击 " .. string.format("%.1f", critChance) .. "%"
            end
        end

        return report
    end


    -- 使用新的详细属性报告
    return GetStatReport()
end



-- 刷新聊天条按钮（当插件加载状态改变时调用）
function WChat:RefreshChatBarButtons()
    if not WChatBar then return end

    -- 清理所有现有按钮，避免拖影
    local children = {WChatBar:GetChildren()}
    for i = 1, #children do
        local child = children[i]
        if child and child:GetObjectType() == "Button" then
            child:Hide()
            child:SetParent(nil)  -- 完全移除父级关系
            child = nil
        end
    end

    -- 强制清理帧缓存
    WChatBar:SetWidth(WChatBar:GetWidth())
    WChatBar:SetHeight(WChatBar:GetHeight())

    -- 重新创建按钮
    local buttonIndex = 1
    for i = 1, #ChannelButtons do
        local buttonData = ChannelButtons[i]
        local shouldShow = true

        -- 检查插件按钮是否应该显示（必须已加载才显示）
        if buttonData.name == "biaoge" then
            shouldShow = WChat_Config.ShowBiaoGeButton and IsAddOnLoaded("BiaoGe")
        elseif buttonData.name == "biaogeai" then
            shouldShow = WChat_Config.ShowBiaoGeAIButton and IsAddOnLoaded("BiaoGeAI")
        elseif buttonData.name == "atlas" then
            shouldShow = WChat_Config.ShowAtlasButton and IsAddOnLoaded("AtlasLootClassic")
        elseif buttonData.name == "meetinghorn" then
            shouldShow = WChat_Config.ShowMeetingHornButton and IsAddOnLoaded("MeetingHorn")
        end

        if shouldShow then
            CreateChannelButton(buttonData, buttonIndex)
            buttonIndex = buttonIndex + 1
        end
    end

    -- 延迟刷新布局，确保所有更改都已应用
    C_Timer.After(0.05, function()
        if WChatBar then
            WChatBar:SetFrameLevel(WChatBar:GetFrameLevel())
        end
    end)

    print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r 聊天条按钮已刷新")
end

-- 公共刷新聊天条函数，供配置界面调用
function WChat:RefreshChatBar()
    self:RefreshChatBarButtons()
end

-- 更新聊天条布局（避免闪现）
function WChat:UpdateChatBarLayout()
    if not WChatBar then return end

    -- 清除所有锚点
    WChatBar:ClearAllPoints()

    -- 根据垂直布局设置尺寸
    if WChat_Config.UseVertical then
        WChatBar:SetWidth(30)
        -- 计算按钮排列的实际范围：第一个按钮到最后一个按钮的距离
        local numButtons = #ChannelButtons
        if numButtons > 0 then
            local buttonRange = math.abs((numButtons - 1) * WChat_Config.DistanceVertical)
            local totalHeight = buttonRange + 30  -- 按钮范围 + 边距
            WChatBar:SetHeight(totalHeight)
        else
            WChatBar:SetHeight(30)
        end
    else
        WChatBar:SetWidth(#ChannelButtons * WChat_Config.DistanceHorizontal + 10)
        WChatBar:SetHeight(30)
    end

    -- 重新定位聊天条
    if WChat_Config.Position == nil then
        if WChat_Config.UseVertical then
            WChatBar:SetPoint("TOPLEFT", "ChatFrame1", "TOPRIGHT", WChat_Config.ChatBarOffsetX + 30, WChat_Config.ChatBarOffsetY + 25)
        else
            WChatBar:SetPoint("TOPLEFT", "ChatFrame1", "BOTTOMLEFT", WChat_Config.ChatBarOffsetX, WChat_Config.ChatBarOffsetY - 30)
        end
    else
        local point = WChat_Config.Position.point
        local relativeTo = WChat_Config.Position.relativeTo
        local relativePoint = WChat_Config.Position.relativePoint
        local xOfs = WChat_Config.Position.xOfs
        local yOfs = WChat_Config.Position.yOfs
        WChatBar:SetPoint(point, relativeTo, relativePoint, xOfs, yOfs)
    end

    -- 重新定位所有按钮
    self:UpdateButtonPositions()
end

-- 专门用于更新按钮位置的函数（用于间距调整时的快速更新）
function WChat:UpdateButtonPositions()
    if not WChatBar then return end

    local numButtons = #ChannelButtons
    if numButtons == 0 then return end

    -- 更新聊天条尺寸
    if WChat_Config.UseVertical then
        -- 计算按钮排列的实际范围：第一个按钮到最后一个按钮的距离
        local buttonRange = math.abs((numButtons - 1) * WChat_Config.DistanceVertical)
        local totalHeight = buttonRange + 30  -- 按钮范围 + 边距
        WChatBar:SetHeight(totalHeight)
    else
        WChatBar:SetWidth(numButtons * WChat_Config.DistanceHorizontal + 10)
    end

    -- 重新定位所有按钮
    for i = 1, WChatBar:GetNumChildren() do
        local child = select(i, WChatBar:GetChildren())
        if child and child:GetObjectType() == "Button" then
            child:ClearAllPoints()
            if WChat_Config.UseVertical then
                -- 垂直布局：始终以第一个按钮为基准，根据间距正负值向不同方向排列
                child:SetPoint("TOP", WChatBar, "TOP", 0, (1 - i) * WChat_Config.DistanceVertical)
            else
                child:SetPoint("LEFT", WChatBar, "LEFT", 10 + (i - 1) * WChat_Config.DistanceHorizontal, 0)
            end
        end
    end
end

-- 更新输入框位置
function WChat:UpdateInputBoxPosition()
    if not inputbox then return end

    inputbox:ClearAllPoints()
    if WChat_Config.UseTopInput then
        inputbox:SetPoint("BOTTOMLEFT", chatFrame, "TOPLEFT", 0, 20)
        inputbox:SetPoint("BOTTOMRIGHT", chatFrame, "TOPRIGHT", 0, 20)
    else
        -- 恢复默认位置
        inputbox:SetPoint("TOPLEFT", chatFrame, "BOTTOMLEFT", -5, -2)
        inputbox:SetPoint("TOPRIGHT", chatFrame, "BOTTOMRIGHT", 5, -2)
    end
end

-- 更新聊天条透明度
function WChat:UpdateChatBarAlpha()
    if not WChatBar then return end

    -- 更新所有按钮的透明度设置
    for i = 1, WChatBar:GetNumChildren() do
        local child = select(i, WChatBar:GetChildren())
        if child and child:GetObjectType() == "Button" then
            child:SetAlpha(WChat_Config.AlphaOnLeave)

            -- 重新设置鼠标事件
            child:SetScript("OnEnter", function(self)
                self:SetAlpha(WChat_Config.AlphaOnEnter)
                if self.tooltip then
                    GameTooltip:SetOwner(self, "ANCHOR_RIGHT")
                    GameTooltip:SetText(self.tooltip, 1, 1, 1, 1, true)
                    GameTooltip:Show()
                end
            end)

            child:SetScript("OnLeave", function(self)
                self:SetAlpha(WChat_Config.AlphaOnLeave)
                GameTooltip:Hide()
            end)
        end
    end
end

-- 模块初始化函数
function WChat:Initialize()
    print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r 开始初始化聊天增强模块...")

    -- 首先保存原始时间戳格式
    if originalTimestampFormat == nil then
        local currentFormat = GetCVar("showTimestamps");
        if currentFormat and not currentFormat:find("|Haccopy:") then
            -- 当前格式不包含复制链接，保存为原始格式
            originalTimestampFormat = currentFormat;
        else
            -- 如果已经有复制链接或者没有格式，默认为none
            originalTimestampFormat = "none";
        end
        print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r 保存原始时间戳格式: " .. tostring(originalTimestampFormat))
    end

    -- 逐步初始化各个子模块，便于排查问题
    local success, err = pcall(function()
        print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r 初始化频道功能...")
        self:InitChannel()

        print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r 初始化表情功能...")
        self:InitEmoteTableFrame()

        print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r 初始化聊天条...")
        self:InitChatBar()

        print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r 初始化时间戳复制...")
        self:InitTimestampCopy()
    end)

    if success then
        print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r 聊天增强模块加载完成")
    else
        print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r 模块初始化失败: " .. tostring(err))
    end
end

-- 配置保存和加载 - 统一使用WanTinyDB数据源
function WChat:LoadConfig()
    -- 确保WanTinyDB结构存在
    _G.WanTinyDB = _G.WanTinyDB or {}
    _G.WanTinyDB.config = _G.WanTinyDB.config or {}
    _G.WanTinyDB.config.WChat = _G.WanTinyDB.config.WChat or {}

    local savedConfig = _G.WanTinyDB.config.WChat

    local WChat_Config = _G.WChat_Config

    -- 从统一数据源加载配置
    for key, value in pairs(savedConfig) do
        if WChat_Config[key] ~= nil then
            WChat_Config[key] = value
        end
    end

    -- 兼容旧的WChatDB数据源
    if WChatDB then
        for key, value in pairs(WChatDB) do
            if WChat_Config[key] ~= nil then
                WChat_Config[key] = value
                -- 迁移到新数据源
                savedConfig[key] = value
            end
        end
        -- 清理旧数据源
        WChatDB = nil
    end
end

function WChat:SaveConfig()
    -- 确保WanTinyDB结构存在
    _G.WanTinyDB = _G.WanTinyDB or {}
    _G.WanTinyDB.config = _G.WanTinyDB.config or {}
    _G.WanTinyDB.config.WChat = _G.WanTinyDB.config.WChat or {}

    local WChat_Config = _G.WChat_Config

    -- 保存到统一数据源
    for key, value in pairs(WChat_Config) do
        _G.WanTinyDB.config.WChat[key] = value
    end
end

-- 防止重复初始化
local WChat_Initialized = false

-- 事件处理
local WChatFrame = CreateFrame("Frame")
WChatFrame:RegisterEvent("PLAYER_LOGOUT")
WChatFrame:SetScript("OnEvent", function(self, event, addonName)
    if event == "PLAYER_LOGOUT" then
        WChat:SaveConfig()
    end
end)

-- 注册到WanTiny模块系统
if WanTiny_RegisterModule then
    WanTiny_RegisterModule("WChat", function()
        if not WChat_Initialized then
            WChat_Initialized = true
            print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r WChat模块开始注册...")

            local success, err = pcall(function()
                WChat:LoadConfig()
                WChat:Initialize()
            end)
            if not success then
                print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r 模块加载失败: " .. tostring(err))
            end
        end
    end)
else
    print("|cffffe00a<|r|cffff7d0aWChat|r|cffffe00a>|r WanTiny_RegisterModule 函数不存在")
end

-- 暴露WChat模块到全局，供配置界面调用
_G.WChat = WChat

-- 创建本地引用以简化代码
local WChat_Config = _G.WChat_Config





